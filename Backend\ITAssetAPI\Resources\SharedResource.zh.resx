<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>用户名或密码错误</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>用户不存在</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>登录成功</value>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>无效的用户令牌</value>
  </data>
  <data name="TokenExpired" xml:space="preserve">
    <value>令牌已过期</value>
  </data>
  
  <!-- Ticket Messages -->
  <data name="TicketNotFound" xml:space="preserve">
    <value>工单不存在</value>
  </data>
  <data name="TicketCreated" xml:space="preserve">
    <value>工单创建成功</value>
  </data>
  <data name="TicketUpdated" xml:space="preserve">
    <value>工单更新成功</value>
  </data>
  <data name="TicketAssigned" xml:space="preserve">
    <value>工单分配成功</value>
  </data>
  <data name="TicketStatusUpdated" xml:space="preserve">
    <value>工单状态更新成功</value>
  </data>
  <data name="InvalidTicketStatus" xml:space="preserve">
    <value>无效的工单状态</value>
  </data>
  <data name="CannotChangeClosedTicketStatus" xml:space="preserve">
    <value>已关闭的工单不能再切换状态</value>
  </data>
  <data name="RollbackChanceAlreadyUsed" xml:space="preserve">
    <value>已使用过一次回溯机会</value>
  </data>
  <data name="CannotCloseFromCurrentStatus" xml:space="preserve">
    <value>当前状态不允许关闭</value>
  </data>
  
  <!-- Comment Messages -->
  <data name="CommentAdded" xml:space="preserve">
    <value>评论添加成功</value>
  </data>
  <data name="CommentDeleted" xml:space="preserve">
    <value>评论删除成功</value>
  </data>
  <data name="CommentNotFound" xml:space="preserve">
    <value>评论不存在</value>
  </data>
  <data name="CannotDeleteOthersComment" xml:space="preserve">
    <value>不能删除其他用户的评论</value>
  </data>
  <data name="CommentsDeleted" xml:space="preserve">
    <value>成功删除了 {0} 条评论</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>此字段为必填项</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>邮箱格式无效</value>
  </data>
  <data name="PasswordTooShort" xml:space="preserve">
    <value>密码长度至少为6位</value>
  </data>
  
  <!-- General Messages -->
  <data name="OperationSuccessful" xml:space="preserve">
    <value>操作成功</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>操作失败</value>
  </data>
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>未授权访问</value>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>服务器内部错误</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>请求无效</value>
  </data>

  <!-- Activity Log Messages -->
  <data name="ActivityLogExportFileName" xml:space="preserve">
    <value>活动日志_{0}.xlsx</value>
  </data>
  <data name="ActivityLogLoadFailed" xml:space="preserve">
    <value>加载活动日志失败</value>
  </data>
  <data name="ActivityLogExportFailed" xml:space="preserve">
    <value>导出活动日志失败</value>
  </data>
  <data name="ActivityLogNotFound" xml:space="preserve">
    <value>未找到活动日志</value>
  </data>

  <!-- Activity Type Display Names -->
  <data name="ActivityTypeCreate" xml:space="preserve">
    <value>创建</value>
  </data>
  <data name="ActivityTypeUpdate" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="ActivityTypeDelete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="ActivityTypeAssign" xml:space="preserve">
    <value>分配</value>
  </data>
  <data name="ActivityTypeUnassign" xml:space="preserve">
    <value>取消分配</value>
  </data>
  <data name="ActivityTypeMaintenance" xml:space="preserve">
    <value>维护</value>
  </data>
  <data name="ActivityTypeDispose" xml:space="preserve">
    <value>报废</value>
  </data>

  <!-- Activity Description Messages -->
  <data name="CreatedAsset" xml:space="preserve">
    <value>创建了资产</value>
  </data>
  <data name="UpdatedAsset" xml:space="preserve">
    <value>更新了资产</value>
  </data>
  <data name="DeletedAsset" xml:space="preserve">
    <value>删除了资产</value>
  </data>
  <data name="AssignedAsset" xml:space="preserve">
    <value>资产被分配给</value>
  </data>
  <data name="UnassignedAsset" xml:space="preserve">
    <value>资产从</value>
  </data>
  <data name="SetAssetToMaintenance" xml:space="preserve">
    <value>资产设为维护状态</value>
  </data>
  <data name="SetAssetToRetired" xml:space="preserve">
    <value>资产设为报废状态</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>分配给</value>
  </data>
  <data name="UnassignedFrom" xml:space="preserve">
    <value>取消分配</value>
  </data>
  <data name="AndSetToMaintenance" xml:space="preserve">
    <value>并设为维护状态</value>
  </data>
  <data name="AndSetToRetired" xml:space="preserve">
    <value>并设为报废状态</value>
  </data>
  <data name="AndUpdated" xml:space="preserve">
    <value>并更新</value>
  </data>
  <data name="And" xml:space="preserve">
    <value>并</value>
  </data>

  <!-- Field Names -->
  <data name="Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>分类</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>品牌</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>型号</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>价值</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="NoValue" xml:space="preserve">
    <value>无</value>
  </data>

  <!-- Status Values -->
  <data name="StatusAvailable" xml:space="preserve">
    <value>可用</value>
  </data>
  <data name="StatusAssigned" xml:space="preserve">
    <value>已分配</value>
  </data>
  <data name="StatusMaintenance" xml:space="preserve">
    <value>维护中</value>
  </data>
  <data name="StatusRetired" xml:space="preserve">
    <value>已报废</value>
  </data>

  <!-- Category Values -->
  <data name="CategoryLaptop" xml:space="preserve">
    <value>笔记本电脑</value>
  </data>
  <data name="CategoryDesktop" xml:space="preserve">
    <value>台式电脑</value>
  </data>
  <data name="CategoryMonitor" xml:space="preserve">
    <value>显示器</value>
  </data>
  <data name="CategoryPrinter" xml:space="preserve">
    <value>打印机</value>
  </data>
  <data name="CategoryPhone" xml:space="preserve">
    <value>电话</value>
  </data>
  <data name="CategoryTablet" xml:space="preserve">
    <value>平板电脑</value>
  </data>
  <data name="CategoryServer" xml:space="preserve">
    <value>服务器</value>
  </data>
  <data name="CategoryNetwork" xml:space="preserve">
    <value>网络设备</value>
  </data>
  <data name="CategoryOther" xml:space="preserve">
    <value>其他</value>
  </data>
</root>
