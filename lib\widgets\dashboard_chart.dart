import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/dashboard_stats.dart';
import '../config/theme.dart';

class Dashboard<PERSON>hart extends StatelessWidget {
  final DashboardStats stats;

  const DashboardChart({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.statusDistribution,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: <PERSON><PERSON><PERSON>(
                      PieChartData(
                        sections: _buildPieChartSections(),
                        centerSpaceRadius: 40,
                        sectionsSpace: 2,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _buildLegend(context),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              l10n.categoryDistribution,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 150,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: _getMaxCategoryCount().toDouble(),
                  barTouchData: BarTouchData(enabled: false),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final topCategories = _getTopCategories();
                          if (value.toInt() < topCategories.length) {
                            final category = topCategories[value.toInt()].category;
                            return Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                _formatCategoryName(category),
                                style: const TextStyle(fontSize: 10),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  gridData: const FlGridData(show: false),
                  borderData: FlBorderData(show: false),
                  barGroups: _buildBarChartGroups(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections() {
    final total = stats.totalAssets;
    if (total == 0) return [];

    return [
      PieChartSectionData(
        color: AppTheme.availableColor,
        value: stats.availableAssets.toDouble(),
        title: '${((stats.availableAssets / total) * 100).toInt()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: AppTheme.assignedColor,
        value: stats.assignedAssets.toDouble(),
        title: '${((stats.assignedAssets / total) * 100).toInt()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: AppTheme.maintenanceColor,
        value: stats.maintenanceAssets.toDouble(),
        title: '${((stats.maintenanceAssets / total) * 100).toInt()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: AppTheme.retiredColor,
        value: stats.retiredAssets.toDouble(),
        title: '${((stats.retiredAssets / total) * 100).toInt()}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  List<Widget> _buildLegend(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return [
      _buildLegendItem(context, l10n.unassigned, AppTheme.availableColor, stats.availableAssets),
      _buildLegendItem(context, l10n.assigned, AppTheme.assignedColor, stats.assignedAssets),
      _buildLegendItem(context, l10n.maintenance, AppTheme.maintenanceColor, stats.maintenanceAssets),
      _buildLegendItem(context, l10n.retired, AppTheme.retiredColor, stats.retiredAssets),
    ];
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color, int count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$label ($count)',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  List<BarChartGroupData> _buildBarChartGroups() {
    // 只显示前3个最多的类别
    final topCategories = _getTopCategories();

    return topCategories.asMap().entries.map((entry) {
      final index = entry.key;
      final categoryData = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: categoryData.count.toDouble(),
            color: AppTheme.primaryColor,
            width: 16,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      );
    }).toList();
  }

  // 获取前3个最多的类别
  List<CategoryStats> _getTopCategories() {
    final sortedCategories = List<CategoryStats>.from(stats.categoryStats);
    sortedCategories.sort((a, b) => b.count.compareTo(a.count));
    return sortedCategories.take(3).toList();
  }

  // 格式化类别名称，避免过长的名称
  String _formatCategoryName(String category) {
    // 如果名称太长，智能截断
    if (category.length <= 8) {
      return category;
    }

    // 尝试在空格处截断
    final words = category.split(' ');
    if (words.length > 1) {
      String result = words[0];
      for (int i = 1; i < words.length; i++) {
        if ((result + ' ' + words[i]).length <= 8) {
          result += ' ' + words[i];
        } else {
          break;
        }
      }
      return result.length < category.length ? '$result...' : result;
    }

    // 如果没有空格，直接截断
    return '${category.substring(0, 8)}...';
  }

  int _getMaxCategoryCount() {
    final topCategories = _getTopCategories();
    if (topCategories.isEmpty) return 10;
    return topCategories
        .map((e) => e.count)
        .reduce((a, b) => a > b ? a : b);
  }
}
