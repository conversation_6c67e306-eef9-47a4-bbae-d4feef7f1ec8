import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/asset.dart';

class AssetCard extends StatelessWidget {
  final Asset asset;
  final VoidCallback? onTap;

  const AssetCard({
    super.key,
    required this.asset,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[100]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 资产基本信息行
                Row(
                  children: [
                    // 资产图标
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(asset.category).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: _getCategoryColor(asset.category).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        _getCategoryIcon(asset.category),
                        color: _getCategoryColor(asset.category),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 资产名称和编号
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            asset.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              fontSize: 18,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            asset.assetNumber,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 状态标签
                    _buildStatusChip(context, asset.status),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 资产详细信息
                if (asset.brand != null || asset.model != null) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          [asset.brand, asset.model].where((e) => e != null && e.isNotEmpty).join(' '),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                
                // 位置信息
                if (asset.location != null && asset.location!.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_rounded,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          asset.location!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                
                // 标签行
                Wrap(
                  spacing: 12,
                  runSpacing: 10,
                  children: [
                    _buildCategoryChip(context, asset.category),
                    if (asset.assignedTo != null && asset.assignedTo!.isNotEmpty)
                      _buildAssignedChip(asset.assignedTo!),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, AssetStatus status) {
    final l10n = AppLocalizations.of(context)!;
    MaterialColor color;
    IconData icon;
    String label;

    switch (status) {
      case AssetStatus.available:
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        label = l10n.available;
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        icon = Icons.person_rounded;
        label = l10n.assigned;
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        icon = Icons.build_rounded;
        label = l10n.maintenance;
        break;
      case AssetStatus.retired:
        color = Colors.grey;
        icon = Icons.delete_forever_rounded;
        label = l10n.retired;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color[700],
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: color[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(BuildContext context, AssetCategory category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getCategoryColor(category).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getCategoryColor(category).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getCategoryIcon(category),
            size: 14,
            color: _getCategoryColor(category)[700],
          ),
          const SizedBox(width: 6),
          Text(
            _getCategoryDisplayName(context, category),
            style: TextStyle(
              color: _getCategoryColor(category)[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignedChip(String assignedTo) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.indigo.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.indigo.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.person_rounded,
            size: 14,
            color: Colors.indigo[700],
          ),
          const SizedBox(width: 6),
          Text(
            assignedTo,
            style: TextStyle(
              color: Colors.indigo[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  MaterialColor _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.green;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.teal;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.cyan;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Icons.laptop;
      case AssetCategory.desktop:
        return Icons.computer;
      case AssetCategory.monitor:
        return Icons.monitor;
      case AssetCategory.printer:
        return Icons.print;
      case AssetCategory.phone:
        return Icons.phone_android;
      case AssetCategory.tablet:
        return Icons.tablet;
      case AssetCategory.server:
        return Icons.dns;
      case AssetCategory.network:
        return Icons.router;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(BuildContext context, AssetCategory category) {
    final l10n = AppLocalizations.of(context)!;
    switch (category) {
      case AssetCategory.laptop:
        return l10n.laptop;
      case AssetCategory.desktop:
        return l10n.desktop;
      case AssetCategory.monitor:
        return l10n.monitor;
      case AssetCategory.printer:
        return l10n.printer;
      case AssetCategory.phone:
        return l10n.phone;
      case AssetCategory.tablet:
        return l10n.tablet;
      case AssetCategory.server:
        return l10n.server;
      case AssetCategory.network:
        return l10n.networkEquipment;
      case AssetCategory.other:
        return l10n.other;
    }
  }
}
