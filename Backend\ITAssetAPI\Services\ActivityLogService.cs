using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using ITAssetAPI.Data;
using ITAssetAPI.DTOs;
using ITAssetAPI.Models;
using ITAssetAPI.Resources;
using System.Text.Json;
using System.Globalization;

namespace ITAssetAPI.Services
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly ApplicationDbContext _context;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public ActivityLogService(ApplicationDbContext context, IStringLocalizer<SharedResource> localizer)
        {
            _context = context;
            _localizer = localizer;
        }

        public async Task<ActivityLogListResponseDto> GetActivityLogsAsync(int page, int limit, string? search = null, string? activityType = null, int? assetId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            Console.WriteLine($"=== GetActivityLogsAsync ===");
            Console.WriteLine($"Page: {page}, Limit: {limit}");
            Console.WriteLine($"Search: {search}");
            Console.WriteLine($"ActivityType: {activityType}");
            Console.WriteLine($"AssetId: {assetId}");
            Console.WriteLine($"StartDate: {startDate}");
            Console.WriteLine($"EndDate: {endDate}");
            
            var query = _context.ActivityLogs.AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLower();
                Console.WriteLine($"=== 搜索逻辑 ===");
                Console.WriteLine($"原始搜索词: '{search}'");
                Console.WriteLine($"小写搜索词: '{searchLower}'");

                // 检查是否是LOG前缀搜索（如：LOG、LOG000042、LOG42等）
                if (searchLower.StartsWith("log"))
                {
                    Console.WriteLine("检测到LOG前缀搜索");
                    // 提取LOG后面的数字部分
                    var numberPart = search.Substring(3); // 去掉"LOG"前缀
                    Console.WriteLine($"数字部分: '{numberPart}'");

                    if (int.TryParse(numberPart, out int logId))
                    {
                        // 精确ID匹配
                        Console.WriteLine($"精确ID匹配: {logId}");
                        query = query.Where(a => a.Id == logId);
                    }
                    else if (string.IsNullOrEmpty(numberPart))
                    {
                        // 只输入了"LOG"，搜索所有记录（因为所有记录都有LOG前缀）
                        Console.WriteLine("只输入LOG，显示所有记录");
                        // 不添加额外过滤条件
                    }
                    else
                    {
                        // LOG后面跟着非数字字符，按普通文本搜索
                        Console.WriteLine("LOG后跟非数字，按文本搜索");
                        query = query.Where(a =>
                            a.Description.Contains(search) ||
                            (a.AssetName != null && a.AssetName.Contains(search)) ||
                            (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                    }
                }
                // 检查是否是纯数字搜索
                else if (int.TryParse(search, out int searchId))
                {
                    // 支持部分数字匹配：搜索ID包含该数字的记录
                    var searchString = searchId.ToString();
                    Console.WriteLine($"纯数字搜索: {search} -> 搜索ID包含'{searchString}'的记录");

                    query = query.Where(a =>
                        a.Id.ToString().Contains(searchString) ||
                        a.Description.Contains(search) ||
                        (a.AssetName != null && a.AssetName.Contains(search)) ||
                        (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                }
                else
                {
                    // 普通文本搜索
                    Console.WriteLine("普通文本搜索");
                    query = query.Where(a =>
                        a.Description.Contains(search) ||
                        (a.AssetName != null && a.AssetName.Contains(search)) ||
                        (a.AssetNumber != null && a.AssetNumber.Contains(search)));
                }
            }

            // 活动类型过滤（支持多个类型，逗号分隔）
            if (!string.IsNullOrEmpty(activityType))
            {
                var activityTypes = activityType.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(t => t.Trim())
                    .Where(t => Enum.TryParse<ActivityType>(t, true, out _))
                    .Select(t => Enum.Parse<ActivityType>(t, true))
                    .ToList();

                if (activityTypes.Any())
                {
                    Console.WriteLine($"活动类型筛选: {string.Join(", ", activityTypes)}");
                    query = query.Where(a => activityTypes.Contains(a.ActivityType));
                }
            }

            // 资产ID过滤
            if (assetId.HasValue)
            {
                query = query.Where(a => a.AssetId == assetId.Value);
            }

            // 日期范围过滤
            if (startDate.HasValue)
            {
                // 确保开始日期从当天的00:00:00开始，并转换为UTC
                var startOfDay = startDate.Value.Date;
                if (startOfDay.Kind == DateTimeKind.Unspecified)
                {
                    startOfDay = DateTime.SpecifyKind(startOfDay, DateTimeKind.Utc);
                }
                Console.WriteLine($"应用开始日期过滤: {startDate.Value} -> {startOfDay}");
                query = query.Where(a => a.CreatedAt >= startOfDay);
            }

            if (endDate.HasValue)
            {
                // 确保结束日期到当天的23:59:59结束，并转换为UTC
                var endOfDay = endDate.Value.Date.AddDays(1).AddTicks(-1);
                if (endOfDay.Kind == DateTimeKind.Unspecified)
                {
                    endOfDay = DateTime.SpecifyKind(endOfDay, DateTimeKind.Utc);
                }
                Console.WriteLine($"应用结束日期过滤: {endDate.Value} -> {endOfDay}");
                query = query.Where(a => a.CreatedAt <= endOfDay);
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / limit);

            var activityLogs = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync();

            // 手动映射到DTO，确保所有字段正确传递
            var activityLogDtos = activityLogs.Select(a => ActivityLogDto.FromModel(a)).ToList();

            Console.WriteLine($"Found {activityLogDtos.Count} activity logs");
            foreach (var dto in activityLogDtos)
            {
                Console.WriteLine($"ID: {dto.Id}, Type: {dto.ActivityType}, CreatedAt: {dto.CreatedAt}, Description: {dto.Description}");
            }

            return new ActivityLogListResponseDto
            {
                ActivityLogs = activityLogDtos,
                TotalCount = totalCount,
                Page = page,
                Limit = limit,
                TotalPages = totalPages
            };
        }

        public async Task<ActivityLogDto> CreateActivityLogAsync(CreateActivityLogDto createActivityLogDto)
        {
            // 将次要活动类型序列化为JSON字符串
            string? secondaryTypesJson = null;
            if (createActivityLogDto.SecondaryActivityTypes.Any())
            {
                secondaryTypesJson = JsonSerializer.Serialize(createActivityLogDto.SecondaryActivityTypes.Select(t => t.ToString()).ToList());
            }
            
            var activityLog = new ActivityLog
            {
                ActivityType = createActivityLogDto.ActivityType,
                SecondaryActivityTypes = secondaryTypesJson,
                Description = createActivityLogDto.Description,
                AssetId = createActivityLogDto.AssetId,
                UserId = createActivityLogDto.UserId,
                UserName = createActivityLogDto.UserName,
                AssetName = createActivityLogDto.AssetName,
                AssetNumber = createActivityLogDto.AssetNumber,
                OldValues = createActivityLogDto.OldValues,
                NewValues = createActivityLogDto.NewValues,
                ChangeSummary = createActivityLogDto.ChangeSummary,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();

            return new ActivityLogDto
            {
                Id = activityLog.Id,
                ActivityType = activityLog.ActivityType,
                SecondaryActivityTypes = activityLog.SecondaryActivityTypes,
                Description = activityLog.Description,
                AssetId = activityLog.AssetId,
                UserId = activityLog.UserId,
                UserName = activityLog.UserName,
                AssetName = activityLog.AssetName,
                AssetNumber = activityLog.AssetNumber,
                OldValues = activityLog.OldValues,
                NewValues = activityLog.NewValues,
                ChangeSummary = activityLog.ChangeSummary,
                CreatedAt = activityLog.CreatedAt
            };
        }

        public async Task LogAssetActivityAsync(string activityType, string description, int? assetId, int userId, string userName, string? assetName = null, string? assetNumber = null, object? oldValues = null, object? newValues = null)
        {
            if (!Enum.TryParse<ActivityType>(activityType, true, out var activityTypeEnum))
            {
                throw new ArgumentException($"Invalid activity type: {activityType}");
            }

            var activityLog = new ActivityLog
            {
                ActivityType = activityTypeEnum,
                Description = description,
                AssetId = assetId,
                UserId = userId,
                UserName = userName,
                AssetName = assetName,
                AssetNumber = assetNumber,
                OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
        }

        // 新增：创建组合活动日志
        public async Task LogCombinedAssetActivityAsync(
            ActivityType primaryType,
            List<ActivityType> secondaryTypes,
            int? assetId,
            int userId,
            string userName,
            string? assetName = null,
            string? assetNumber = null,
            object? oldValues = null,
            object? newValues = null)
        {
            Console.WriteLine($"=== LOGGING ACTIVITY ===");
            Console.WriteLine($"Primary Type: {primaryType}");
            Console.WriteLine($"Secondary Types: {string.Join(", ", secondaryTypes)}");
            Console.WriteLine($"Asset: {assetName} ({assetNumber})");
            
            // 生成详细描述
            var description = await GenerateSmartDescription(primaryType, secondaryTypes, assetName, assetNumber, oldValues, newValues);
            Console.WriteLine($"Description: {description}");
            
            // 生成变更摘要
            var changeSummary = GenerateChangeSummary(oldValues, newValues);
            Console.WriteLine($"Change Summary: {changeSummary}");
            
            // 将次要活动类型序列化为JSON字符串
            string? secondaryTypesJson = null;
            if (secondaryTypes.Any())
            {
                secondaryTypesJson = JsonSerializer.Serialize(secondaryTypes.Select(t => t.ToString()).ToList());
                Console.WriteLine($"Secondary Types JSON: {secondaryTypesJson}");
            }
            
            var activityLog = new ActivityLog
            {
                ActivityType = primaryType,
                SecondaryActivityTypes = secondaryTypesJson,
                Description = description,
                AssetId = assetId,
                UserId = userId,
                UserName = userName,
                AssetName = assetName,
                AssetNumber = assetNumber,
                OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                ChangeSummary = changeSummary,
                CreatedAt = DateTime.UtcNow
            };

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();
            
            Console.WriteLine($"Activity log created successfully. ID: {activityLog.Id}");
        }

        // 新增：生成智能描述
        private async Task<string> GenerateSmartDescription(
            ActivityType primaryType,
            List<ActivityType> secondaryTypes,
            string? assetName,
            string? assetNumber,
            object? oldValues,
            object? newValues)
        {
            var assetInfo = !string.IsNullOrEmpty(assetName) ? $"{assetName} ({assetNumber})" : assetNumber ?? "资产";
            
            // 根据主要活动类型生成描述
            switch (primaryType)
            {
                case ActivityType.Create:
                    // 调试国际化
                    Console.WriteLine($"Current Culture: {CultureInfo.CurrentCulture}");
                    Console.WriteLine($"Current UI Culture: {CultureInfo.CurrentUICulture}");
                    Console.WriteLine($"CreatedAsset localized: '{_localizer["Created Asset"]}'");

                    // 简化创建资产的描述，避免过长
                    var createParts = new List<string>();

                    // 检查是否有分配
                    string? assignedToUser = null;
                    if (secondaryTypes.Contains(ActivityType.Assign) && newValues != null)
                    {
                        try
                        {
                            var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                            var assignedTo = newDict!.ContainsKey("AssignedTo") ?
                                newDict["AssignedTo"].ToString() : null;
                            var assignedUserId = newDict.ContainsKey("AssignedUserId") ?
                                newDict["AssignedUserId"].ToString() : null;

                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") &&
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }

                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null")
                            {
                                assignedToUser = assignedTo;
                                createParts.Add($"{_localizer["AssignedTo"]} {assignedTo}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing newValues for assignment: {ex.Message}");
                        }
                    }

                    // 检查是否有状态设置
                    if (secondaryTypes.Contains(ActivityType.Maintenance))
                    {
                        createParts.Add(_localizer["AndSetToMaintenance"]);
                    }
                    else if (secondaryTypes.Contains(ActivityType.Dispose))
                    {
                        createParts.Add(_localizer["AndSetToRetired"]);
                    }

                    // 生成简化描述
                    if (createParts.Count > 0)
                    {
                        var additionalInfo = string.Join(", ", createParts);
                        return $"{_localizer["Created Asset"]} {assetInfo} ({additionalInfo})";
                    }

                    return $"{_localizer["Created Asset"]} {assetInfo}";
                
                case ActivityType.Update:
                    return $"{_localizer["Updated Asset"]} {assetInfo}";

                case ActivityType.Delete:
                    return $"{_localizer["Deleted Asset"]} {assetInfo}";

                case ActivityType.Assign:
                    // 分配描述
                    if (newValues != null)
                    {
                        try
                        {
                            var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                            var assignedTo = newDict!.ContainsKey("AssignedTo") ?
                                newDict["AssignedTo"].ToString() : null;
                            var assignedUserId = newDict.ContainsKey("AssignedUserId") ?
                                newDict["AssignedUserId"].ToString() : null;

                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") &&
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }

                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null" && assignedTo != "")
                            {
                                return $"{_localizer["AssignedAsset"]} {assignedTo} {assetInfo}";
                            }
                        }
                        catch { /* 解析失败，使用默认描述 */ }
                    }
                    return $"{_localizer["AssignedAsset"]} {assetInfo}";
                
                case ActivityType.Unassign:
                    // 取消分配描述
                    if (oldValues != null)
                    {
                        try
                        {
                            var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                            var assignedTo = oldDict!.ContainsKey("AssignedTo") ?
                                oldDict["AssignedTo"].ToString() : null;
                            var assignedUserId = oldDict.ContainsKey("AssignedUserId") ?
                                oldDict["AssignedUserId"].ToString() : null;

                            // 如果AssignedTo为空但AssignedUserId有值，尝试从数据库查询用户名
                            if ((string.IsNullOrEmpty(assignedTo) || assignedTo == "null") &&
                                !string.IsNullOrEmpty(assignedUserId) && assignedUserId != "null" && assignedUserId != "0")
                            {
                                try
                                {
                                    var userId = int.Parse(assignedUserId);
                                    var user = await _context.Users.FindAsync(userId);
                                    if (user != null)
                                    {
                                        assignedTo = user.Username;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error querying user: {ex.Message}");
                                }
                            }

                            if (!string.IsNullOrEmpty(assignedTo) && assignedTo != "null" && assignedTo != "")
                            {
                                return $"{_localizer["UnassignedAsset"]} {assignedTo} {assetInfo}";
                            }
                        }
                        catch { /* 解析失败，使用默认描述 */ }
                    }
                    return $"{_localizer["UnassignedAsset"]} {assetInfo}";

                case ActivityType.Maintenance:
                    return $"{_localizer["Set Asset To Maintenance"]} {assetInfo}";

                case ActivityType.Dispose:
                    return $"{_localizer["Set Asset To Retired"]} {assetInfo}";
            }
            
            // 默认组合描述方式
            var actions = new List<string>();
            
            // 添加主要活动
            actions.Add(GetActivityTypeDisplayName(primaryType));
            
            // 添加次要活动（去重）
            var uniqueSecondaryTypes = secondaryTypes.Distinct().ToList();
            foreach (var secondaryType in uniqueSecondaryTypes)
            {
                if (secondaryType != primaryType) // 避免重复
                {
                    actions.Add(GetActivityTypeDisplayName(secondaryType));
                }
            }
            
            var actionText = string.Join(" 和 ", actions);

            // 生成具体变更描述
            var changes = GenerateChangeDetails(oldValues, newValues);

            if (!string.IsNullOrEmpty(changes))
            {
                return $"资产 {assetInfo} {actionText}: {changes}";
            }

            return $"资产 {assetInfo} {actionText}";
        }

        // 新增：生成变更详情
        private string GenerateChangeDetails(object? oldValues, object? newValues)
        {
            if (oldValues == null || newValues == null) return string.Empty;
            
            try
            {
                var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                
                var changes = new List<string>();
                
                foreach (var key in newDict.Keys)
                {
                    if (oldDict.ContainsKey(key) && !Equals(oldDict[key], newDict[key]))
                    {
                        var fieldName = GetFieldDisplayName(key);
                        var oldValue = GetDisplayValue(oldDict[key]);
                        var newValue = GetDisplayValue(newDict[key]);
                        
                        if (oldValue != newValue)
                        {
                            changes.Add($"{fieldName}: {oldValue} → {newValue}");
                        }
                    }
                }
                
                return string.Join("，", changes);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 新增：生成变更摘要
        private string GenerateChangeSummary(object? oldValues, object? newValues)
        {
            if (oldValues == null || newValues == null) return string.Empty;
            
            try
            {
                var oldDict = JsonSerializer.Deserialize<Dictionary<string, object>>(oldValues.ToString()!);
                var newDict = JsonSerializer.Deserialize<Dictionary<string, object>>(newValues.ToString()!);
                
                var keyChanges = new List<string>();
                
                // 检查关键字段的变更
                var keyFields = new[] { "Status", "AssignedTo", "Category" };
                
                foreach (var field in keyFields)
                {
                    if (oldDict.ContainsKey(field) && newDict.ContainsKey(field) && 
                        !Equals(oldDict[field], newDict[field]))
                    {
                        var oldValue = GetDisplayValue(oldDict[field]);
                        var newValue = GetDisplayValue(newDict[field]);
                        keyChanges.Add($"{oldValue} → {newValue}");
                    }
                }
                
                return string.Join("，", keyChanges);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 新增：获取字段显示名称
        private string GetFieldDisplayName(string fieldName)
        {
            // 使用当前文化的本地化字符串
            return fieldName switch
            {
                "Status" => _localizer["Status"].Value,
                "AssignedTo" => _localizer["AssignedTo"].Value,
                "Category" => _localizer["Category"].Value,
                "Name" => _localizer["Name"].Value,
                "Brand" => _localizer["Brand"].Value,
                "Model" => _localizer["Model"].Value,
                "Location" => _localizer["Location"].Value,
                "Value" => _localizer["Value"].Value,
                "Description" => _localizer["Description"].Value,
                _ => fieldName
            };
        }

        // 新增：获取显示值
        private string GetDisplayValue(object? value)
        {
            if (value == null) return _localizer["NoValue"].Value;

            var stringValue = value.ToString();

            // 处理状态枚举
            if (Enum.TryParse<AssetStatus>(stringValue, out var status))
            {
                return status switch
                {
                    AssetStatus.Available => _localizer["StatusAvailable"].Value,
                    AssetStatus.Assigned => _localizer["StatusAssigned"].Value,
                    AssetStatus.Maintenance => _localizer["StatusMaintenance"].Value,
                    AssetStatus.Retired => _localizer["StatusRetired"].Value,
                    _ => stringValue
                };
            }

            // 处理分类枚举
            if (Enum.TryParse<AssetCategory>(stringValue, out var category))
            {
                return category switch
                {
                    AssetCategory.Laptop => _localizer["CategoryLaptop"].Value,
                    AssetCategory.Desktop => _localizer["CategoryDesktop"].Value,
                    AssetCategory.Monitor => _localizer["CategoryMonitor"].Value,
                    AssetCategory.Printer => _localizer["CategoryPrinter"].Value,
                    AssetCategory.Phone => _localizer["CategoryPhone"].Value,
                    AssetCategory.Tablet => _localizer["CategoryTablet"].Value,
                    AssetCategory.Server => _localizer["CategoryServer"].Value,
                    AssetCategory.Network => _localizer["CategoryNetwork"].Value,
                    AssetCategory.Other => _localizer["CategoryOther"].Value,
                    _ => stringValue
                };
            }

            return stringValue ?? _localizer["NoValue"].Value;
        }

        // 新增：获取活动类型显示名称
        private string GetActivityTypeDisplayName(ActivityType type)
        {
            return type switch
            {
                ActivityType.Create => _localizer["ActivityTypeCreate"].Value,
                ActivityType.Update => _localizer["ActivityTypeUpdate"].Value,
                ActivityType.Delete => _localizer["ActivityTypeDelete"].Value,
                ActivityType.Assign => _localizer["ActivityTypeAssign"].Value,
                ActivityType.Unassign => _localizer["ActivityTypeUnassign"].Value,
                ActivityType.Maintenance => _localizer["ActivityTypeMaintenance"].Value,
                ActivityType.Dispose => _localizer["ActivityTypeDispose"].Value,
                _ => type.ToString()
            };
        }
    }
} 