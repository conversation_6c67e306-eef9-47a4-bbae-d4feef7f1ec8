import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../widgets/user_main_layout.dart';
import '../models/asset.dart';
import '../services/api_service.dart';
import '../utils/timezone_utils.dart';

class UserAssetDetailScreen extends StatefulWidget {
  final String assetId;

  const UserAssetDetailScreen({
    super.key,
    required this.assetId,
  });

  @override
  State<UserAssetDetailScreen> createState() => _UserAssetDetailScreenState();
}

class _UserAssetDetailScreenState extends State<UserAssetDetailScreen> {
  final ApiService _apiService = ApiService();
  Asset? _asset;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAsset();
  }

  Future<void> _loadAsset() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final asset = await _apiService.getAsset(widget.assetId);
      
      if (mounted) {
        setState(() {
          _asset = asset;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return UserMainLayout(
      currentRoute: '/user-assets/${widget.assetId}',
      title: l10n.userAssetDetails,
      showBackButton: true,
      onBackPressed: () {
        // 返回到用户资产列表页面
        context.go('/user-assets');
      },
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    final l10n = AppLocalizations.of(context)!;
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.loadingFailed,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAsset,
              child: Text(l10n.retryLoading),
            ),
          ],
        ),
      );
    }

    if (_asset == null) {
      return Center(
        child: Text(l10n.assetNotExists),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAssetHeader(),
          const SizedBox(height: 20),
          _buildBasicInfo(),
          const SizedBox(height: 20),
          _buildTechnicalInfo(),
          const SizedBox(height: 20),
          _buildLocationInfo(),
          const SizedBox(height: 20),
          _buildStatusInfo(),
        ],
      ),
    );
  }

  Widget _buildAssetHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getCategoryColor(_asset!.category),
            _getCategoryColor(_asset!.category).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _getCategoryColor(_asset!.category).withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(_asset!.category),
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _asset!.name,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _asset!.assetNumber,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusBadge(),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getCategoryDisplayName(_asset!.category),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    final l10n = AppLocalizations.of(context)!;
    Color color;
    String label;

    switch (_asset!.status) {
      case AssetStatus.available:
        color = Colors.green;
        label = l10n.available;
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        label = l10n.assigned;
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        label = l10n.maintenance;
        break;
      case AssetStatus.retired:
        color = Colors.grey;
        label = l10n.retired;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    final l10n = AppLocalizations.of(context)!;
    return _buildInfoCard(
      title: l10n.basicInfo,
      icon: Icons.info_outline,
      children: [
        _buildInfoRow(l10n.assetName, _asset!.name),
        _buildInfoRow(l10n.assetNumber, _asset!.assetNumber),
        _buildInfoRow(l10n.serialNumber, _asset!.serialNumber?.isNotEmpty == true ? _asset!.serialNumber! : l10n.notSet),
        _buildInfoRow(l10n.brand, _asset!.brand?.isNotEmpty == true ? _asset!.brand! : l10n.notSet),
        _buildInfoRow(l10n.model, _asset!.model?.isNotEmpty == true ? _asset!.model! : l10n.notSet),
      ],
    );
  }

  Widget _buildTechnicalInfo() {
    final l10n = AppLocalizations.of(context)!;
    return _buildInfoCard(
      title: l10n.technicalInfo,
      icon: Icons.settings,
      children: [
        _buildInfoRow(l10n.category, _getCategoryDisplayName(_asset!.category)),
        _buildInfoRow(l10n.status, _getStatusDisplayName(_asset!.status)),
        _buildInfoRow(l10n.purchaseDate, _asset!.purchaseDate != null ? _formatDate(_asset!.purchaseDate!) : l10n.notSpecified),
        _buildInfoRow(l10n.assignedTo, _asset!.assignedTo?.isNotEmpty == true ? _asset!.assignedTo! : l10n.unassigned),
        _buildInfoRow(l10n.vendor, _asset!.vendor?.isNotEmpty == true ? _asset!.vendor! : l10n.notSpecified),
        _buildInfoRow(l10n.value, _asset!.value != null ? '¥${_asset!.value!.toStringAsFixed(2)}' : l10n.notSpecified),
      ],
    );
  }

  Widget _buildLocationInfo() {
    final l10n = AppLocalizations.of(context)!;
    return _buildInfoCard(
      title: l10n.locationInfo,
      icon: Icons.location_on,
      children: [
        _buildInfoRow(l10n.location, _asset!.location?.isNotEmpty == true ? _asset!.location! : l10n.notSpecified),
        _buildInfoRow(l10n.description, _asset!.description?.isNotEmpty == true ? _asset!.description! : l10n.notSpecified),
      ],
    );
  }

  Widget _buildStatusInfo() {
    final l10n = AppLocalizations.of(context)!;
    return _buildInfoCard(
      title: l10n.statusInfo,
      icon: Icons.timeline,
      children: [
        _buildInfoRow(l10n.createdAtLabel, _asset!.createdAt != null ? _formatDateTime(_asset!.createdAt!) : l10n.notSpecified),
        _buildInfoRow(l10n.updatedAtLabel, _asset!.updatedAt != null ? _formatDateTime(_asset!.updatedAt!) : l10n.notSpecified),
        _buildInfoRow(l10n.lastMaintenanceDate, _asset!.lastMaintenanceDate != null ? _formatDate(_asset!.lastMaintenanceDate!) : l10n.notSpecified),
        _buildInfoRow(l10n.nextMaintenanceDate, _asset!.nextMaintenanceDate != null ? _formatDate(_asset!.nextMaintenanceDate!) : l10n.notSpecified),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.green;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.teal;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.cyan;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Icons.laptop;
      case AssetCategory.desktop:
        return Icons.computer;
      case AssetCategory.monitor:
        return Icons.monitor;
      case AssetCategory.printer:
        return Icons.print;
      case AssetCategory.phone:
        return Icons.phone_android;
      case AssetCategory.tablet:
        return Icons.tablet;
      case AssetCategory.server:
        return Icons.dns;
      case AssetCategory.network:
        return Icons.router;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    final l10n = AppLocalizations.of(context)!;
    switch (category) {
      case AssetCategory.laptop:
        return l10n.laptop;
      case AssetCategory.desktop:
        return l10n.desktop;
      case AssetCategory.monitor:
        return l10n.monitor;
      case AssetCategory.printer:
        return l10n.printer;
      case AssetCategory.phone:
        return l10n.phone;
      case AssetCategory.tablet:
        return l10n.tablet;
      case AssetCategory.server:
        return l10n.server;
      case AssetCategory.network:
        return l10n.networkEquipment;
      case AssetCategory.other:
        return l10n.other;
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    final l10n = AppLocalizations.of(context)!;
    switch (status) {
      case AssetStatus.available:
        return l10n.available;
      case AssetStatus.assigned:
        return l10n.assigned;
      case AssetStatus.maintenance:
        return l10n.maintenance;
      case AssetStatus.retired:
        return l10n.retired;
    }
  }

  String _formatDate(DateTime date) {
    return TimezoneUtils.formatDate(date);
  }

  String _formatDateTime(DateTime dateTime) {
    return TimezoneUtils.formatDateTime(dateTime);
  }
}
