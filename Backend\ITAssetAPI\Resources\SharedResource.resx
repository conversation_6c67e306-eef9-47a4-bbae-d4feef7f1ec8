<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>Invalid username or password</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>Login successful</value>
  </data>
  <data name="InvalidToken" xml:space="preserve">
    <value>Invalid user token</value>
  </data>
  <data name="TokenExpired" xml:space="preserve">
    <value>Token has expired</value>
  </data>
  
  <!-- Ticket Messages -->
  <data name="TicketNotFound" xml:space="preserve">
    <value>Ticket not found</value>
  </data>
  <data name="TicketCreated" xml:space="preserve">
    <value>Ticket created successfully</value>
  </data>
  <data name="TicketUpdated" xml:space="preserve">
    <value>Ticket updated successfully</value>
  </data>
  <data name="TicketAssigned" xml:space="preserve">
    <value>Ticket assigned successfully</value>
  </data>
  <data name="TicketStatusUpdated" xml:space="preserve">
    <value>Ticket status updated successfully</value>
  </data>
  <data name="InvalidTicketStatus" xml:space="preserve">
    <value>Invalid ticket status</value>
  </data>
  <data name="CannotChangeClosedTicketStatus" xml:space="preserve">
    <value>Cannot change status of closed ticket</value>
  </data>
  <data name="RollbackChanceAlreadyUsed" xml:space="preserve">
    <value>Rollback chance has already been used</value>
  </data>
  <data name="CannotCloseFromCurrentStatus" xml:space="preserve">
    <value>Cannot close ticket from current status</value>
  </data>
  
  <!-- Comment Messages -->
  <data name="CommentAdded" xml:space="preserve">
    <value>Comment added successfully</value>
  </data>
  <data name="CommentDeleted" xml:space="preserve">
    <value>Comment deleted successfully</value>
  </data>
  <data name="CommentNotFound" xml:space="preserve">
    <value>Comment not found</value>
  </data>
  <data name="CannotDeleteOthersComment" xml:space="preserve">
    <value>Cannot delete other user's comment</value>
  </data>
  <data name="CommentsDeleted" xml:space="preserve">
    <value>{0} comments deleted successfully</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="PasswordTooShort" xml:space="preserve">
    <value>Password must be at least 6 characters long</value>
  </data>
  
  <!-- General Messages -->
  <data name="OperationSuccessful" xml:space="preserve">
    <value>Operation completed successfully</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>Operation failed</value>
  </data>
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>Internal server error</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>Bad request</value>
  </data>

  <!-- Activity Log Messages -->
  <data name="CreatedAsset" xml:space="preserve">
    <value>Created Asset</value>
  </data>
  <data name="UpdatedAsset" xml:space="preserve">
    <value>Updated Asset</value>
  </data>
  <data name="DeletedAsset" xml:space="preserve">
    <value>Deleted Asset</value>
  </data>
  <data name="AssignedAsset" xml:space="preserve">
    <value>Asset Assigned to</value>
  </data>
  <data name="UnassignedAsset" xml:space="preserve">
    <value>Asset Unassigned from</value>
  </data>
  <data name="SetAssetToMaintenance" xml:space="preserve">
    <value>Asset Set to Maintenance</value>
  </data>
  <data name="SetAssetToRetired" xml:space="preserve">
    <value>Asset Set to Retired</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>Assigned to</value>
  </data>
  <data name="UnassignedFrom" xml:space="preserve">
    <value>Unassigned from</value>
  </data>
  <data name="AndSetToMaintenance" xml:space="preserve">
    <value>and Set to Maintenance</value>
  </data>
  <data name="AndSetToRetired" xml:space="preserve">
    <value>and Set to Retired</value>
  </data>
  <data name="AndUpdated" xml:space="preserve">
    <value>and Updated</value>
  </data>
  <data name="And" xml:space="preserve">
    <value>and</value>
  </data>

  <!-- Field Names -->
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="NoValue" xml:space="preserve">
    <value>None</value>
  </data>

  <!-- Status Values -->
  <data name="StatusAvailable" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="StatusAssigned" xml:space="preserve">
    <value>Assigned</value>
  </data>
  <data name="StatusMaintenance" xml:space="preserve">
    <value>Maintenance</value>
  </data>
  <data name="StatusRetired" xml:space="preserve">
    <value>Retired</value>
  </data>

  <!-- Category Values -->
  <data name="CategoryLaptop" xml:space="preserve">
    <value>Laptop</value>
  </data>
  <data name="CategoryDesktop" xml:space="preserve">
    <value>Desktop</value>
  </data>
  <data name="CategoryMonitor" xml:space="preserve">
    <value>Monitor</value>
  </data>
  <data name="CategoryPrinter" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="CategoryPhone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="CategoryTablet" xml:space="preserve">
    <value>Tablet</value>
  </data>
  <data name="CategoryServer" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="CategoryNetwork" xml:space="preserve">
    <value>Network</value>
  </data>
  <data name="CategoryOther" xml:space="preserve">
    <value>Other</value>
  </data>
</root>
