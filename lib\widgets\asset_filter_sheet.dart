import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/asset.dart';

class AssetFilterSheet extends StatefulWidget {
  final List<String> selectedCategories;
  final List<String> selectedStatuses;
  final Function(List<String> categories, List<String> statuses) onApply;

  const AssetFilterSheet({
    super.key,
    required this.selectedCategories,
    required this.selectedStatuses,
    required this.onApply,
  });

  @override
  State<AssetFilterSheet> createState() => _AssetFilterSheetState();
}

class _AssetFilterSheetState extends State<AssetFilterSheet> {
  late List<String> _selectedCategories;
  late List<String> _selectedStatuses;

  final List<String> _categories = [
    'laptop',
    'desktop',
    'monitor',
    'printer',
    'phone',
    'tablet',
    'server',
    'network',
    'other',
  ];

  final List<String> _statuses = [
    'available',
    'assigned',
    'maintenance',
    'retired',
  ];

  @override
  void initState() {
    super.initState();
    _selectedCategories = List.from(widget.selectedCategories);
    _selectedStatuses = List.from(widget.selectedStatuses);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  l10n.filterAssets,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategories.clear();
                      _selectedStatuses.clear();
                    });
                  },
                  child: Text(l10n.clearAllFilters),
                ),
              ],
            ),
          ),
          
          // 筛选内容
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 资产类别
                  _buildSectionTitle(l10n.assetCategories),
                  const SizedBox(height: 8),
                  _buildCategoryFilters(),

                  const SizedBox(height: 24),

                  // 资产状态
                  _buildSectionTitle(l10n.assetStatuses),
                  const SizedBox(height: 8),
                  _buildStatusFilters(),
                  
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(l10n.cancel),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onApply(_selectedCategories, _selectedStatuses);
                      Navigator.of(context).pop();
                    },
                    child: Text(l10n.applyFilters),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildCategoryFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _categories.map((category) {
        final isSelected = _selectedCategories.contains(category);
        return FilterChip(
          label: Text(_getCategoryDisplayName(category)),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedCategories.add(category);
              } else {
                _selectedCategories.remove(category);
              }
            });
          },
          selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
          showCheckmark: false, // 不显示打勾
        );
      }).toList(),
    );
  }

  Widget _buildStatusFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _statuses.map((status) {
        final isSelected = _selectedStatuses.contains(status);
        return FilterChip(
          label: Text(_getStatusDisplayName(status)),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedStatuses.add(status);
              } else {
                _selectedStatuses.remove(status);
              }
            });
          },
          selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
          showCheckmark: false, // 不显示打勾
        );
      }).toList(),
    );
  }

  String _getCategoryDisplayName(String category) {
    final l10n = AppLocalizations.of(context)!;
    switch (category) {
      case 'laptop':
        return l10n.laptop;
      case 'desktop':
        return l10n.desktop;
      case 'monitor':
        return l10n.monitor;
      case 'printer':
        return l10n.printer;
      case 'phone':
        return l10n.phone;
      case 'tablet':
        return l10n.tablet;
      case 'server':
        return l10n.server;
      case 'network':
        return l10n.networkEquipment;
      case 'other':
        return l10n.other;
      default:
        return category;
    }
  }

  String _getStatusDisplayName(String status) {
    final l10n = AppLocalizations.of(context)!;
    switch (status) {
      case 'available':
        return l10n.available;
      case 'assigned':
        return l10n.assigned;
      case 'maintenance':
        return l10n.maintenance;
      case 'retired':
        return l10n.retired;
      default:
        return status;
    }
  }
}
