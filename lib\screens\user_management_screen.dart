import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../services/excel_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';
import '../widgets/main_layout.dart';
import '../widgets/animated_search_bar.dart';
import '../widgets/success_animation.dart';
import '../providers/auth_provider.dart';
import '../config/routes.dart';
import 'user_form_screen.dart';
import 'user_detail_screen.dart';
import '../utils/timezone_utils.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({Key? key}) : super(key: key);

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> with WidgetsBindingObserver {
  final UserService _userService = UserService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  
  UserListResponse? _userListResponse;
  bool _isLoading = false;
  String? _error;
  bool _showScrollToTop = false;
  bool _isSearching = false;
  bool _searchCompleted = false;
  
  // Filter and pagination state
  List<String> _selectedRoles = []; // 改为列表支持多选
  List<String> _selectedDepartments = []; // 改为列表支持多选
  List<String> _appliedRoles = []; // 实际应用的角色筛选列表
  List<String> _appliedDepartments = []; // 实际应用的部门筛选列表
  int _currentPage = 1;
  final int _pageSize = 20; // 固定每页20条记录
  String _sortBy = 'CreatedAt';
  bool _sortDescending = true;
  
  List<String> _roles = [];
  List<String> _departments = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // 添加生命周期观察者
    // 检查认证状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndInitialize();
    });
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // 移除生命周期观察者
    _searchController.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时，刷新数据
    if (state == AppLifecycleState.resumed && mounted) {
      _loadUsers();
    }
  }

  Future<void> _checkAuthAndInitialize() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // 如果用户未登录，重新初始化认证状态
    if (!authProvider.isLoggedIn) {
      await authProvider.initializeAuth();
    }
    
    // 如果仍然未登录，显示错误信息
    if (!authProvider.isLoggedIn) {
      setState(() {
        _error = AppLocalizations.of(context)!.userNotLoggedIn;
      });
      return;
    }
    
    // 加载初始数据
    await _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadUsers(),
      _loadRoles(),
      _loadDepartments(),
    ]);
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final query = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        roles: _appliedRoles.isNotEmpty ? _appliedRoles : null, // 使用多选角色
        departments: _appliedDepartments.isNotEmpty ? _appliedDepartments : null, // 使用多选部门
        pageNumber: _currentPage,
        pageSize: _pageSize,
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );

      final response = await _userService.getUsers(query);

      // 调试：打印用户数据
      print('=== 用户列表刷新 ===');
      for (var user in response.users) {
        print('用户: ${user.username}, 部门: ${user.department}');
      }

      setState(() {
        _userListResponse = response;
        _isLoading = false;
        _searchCompleted = true;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadRoles() async {
    try {
      final roles = await _userService.getRoles();
      setState(() {
        _roles = roles;
      });
    } catch (e) {
      // Handle error silently for roles
    }
  }

  Future<void> _loadDepartments() async {
    try {
      final departments = await _userService.getDepartments();

      setState(() {
        _departments = departments;
      });
    } catch (e) {

      // Handle error silently for departments
    }
  }

  // 将中文部门名称转换为国际化显示名称
  String _getDepartmentDisplayName(String department) {
    final l10n = AppLocalizations.of(context)!;

    switch (department) {
      case "IT部门":
        return l10n.itDepartment;
      case "人力资源部":
        return l10n.hrDepartment;
      case "财务部":
        return l10n.financeDepartment;
      case "市场营销部":
        return l10n.marketingDepartment;
      case "研发部":
        return l10n.rdDepartment;
      case "销售部":
        return l10n.salesDepartment;
      case "运营部":
        return l10n.operationsDepartment;
      case "客服部":
        return l10n.customerServiceDepartment;
      case "行政部":
        return l10n.administrationDepartment;
      case "法务部":
        return l10n.legalDepartment;
      case "产品部":
        return l10n.productDepartment;
      case "培训部":
        return l10n.trainingDepartment;
      case "安全部":
        return l10n.securityDepartment;
      case "质量管理部":
        return l10n.qualityDepartment;
      case "采购部":
        return l10n.procurementDepartment;
      default:
        return department; // 如果没有匹配的翻译，返回原始名称
    }
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();

    // 立即更新搜索状态
    setState(() {
      _isSearching = _searchController.text.isNotEmpty;
      _searchCompleted = false;
    });

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _currentPage = 1;
      _loadUsers();
    });
  }

  void _onFilterChanged() {
    // 应用筛选时，更新已应用的筛选条件
    setState(() {
      _appliedRoles = List.from(_selectedRoles);
      _appliedDepartments = List.from(_selectedDepartments);
    });
    _currentPage = 1;
    _loadUsers();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadUsers();
  }

  void _onSortChanged(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortDescending = !_sortDescending;
      } else {
        _sortBy = sortBy;
        _sortDescending = true;
      }
    });
    _loadUsers();
  }

  void _onScroll() {
    if (_scrollController.offset >= 400 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset < 400 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedRoles = [];
      _selectedDepartments = [];
      _appliedRoles = [];
      _appliedDepartments = [];
      _isSearching = false;
      _searchCompleted = false;
    });
    _currentPage = 1;
    _loadUsers();
  }

  // 检查是否有已应用的筛选条件
  bool get _hasActiveFilters {
    return (_searchController.text.isNotEmpty && _searchCompleted) ||
           _appliedRoles.isNotEmpty ||
           _appliedDepartments.isNotEmpty;
  }

  Future<void> _deleteUser(User user) async {
    final l10n = AppLocalizations.of(context)!;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.confirmDelete),
        content: Text(l10n.confirmDeleteUser(user.username)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.deleteUser),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.deleteUser(user.id);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: l10n.deleteSuccess,
          message: l10n.userDeletedSuccess(user.username),
          onComplete: () {
            _loadUsers();
          },
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.deleteFailed}: $e')),
        );
      }
    }
  }

  void _navigateToUserForm({User? user}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: user),
      ),
    );

    if (result == true) {
      // 强制刷新用户列表
      await _loadUsers();
      // 确保UI更新
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _navigateToUserDetail(User user) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(user: user),
      ),
    );

    if (result == true) {
      // 强制刷新用户列表
      await _loadUsers();
      // 确保UI更新
      if (mounted) {
        setState(() {});
      }
    }
  }

  Future<void> _showChangeRoleDialog(User user) async {
    final l10n = AppLocalizations.of(context)!;
    String? selectedRole = user.role;

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.changeUserRole(user.username)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(l10n.currentRole(user.role)),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedRole,
              decoration: InputDecoration(
                labelText: l10n.selectNewRole,
                border: const OutlineInputBorder(),
              ),
              items: UserRole.all.map((role) => DropdownMenuItem(
                value: role,
                child: Text(role == 'Admin' ? l10n.administrator : l10n.regularUser),
              )).toList(),
              onChanged: (value) {
                selectedRole = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(selectedRole),
            child: Text(l10n.confirm),
          ),
        ],
      ),
    );

    if (result != null && result != user.role) {
      await _changeUserRole(user, result);
    }
  }

  Future<void> _changeUserRole(User user, String newRole) async {
    final l10n = AppLocalizations.of(context)!;
    try {
      await _userService.changeUserRole(user.id, newRole);

      // 显示现代化的成功动画
      SuccessAnimationOverlay.show(
        context,
        title: l10n.roleChangeSuccess,
        message: l10n.userRoleChanged(user.username, newRole == 'Admin' ? l10n.administrator : l10n.regularUser),
        onComplete: () {
          _loadUsers();
        },
      );
    } catch (e) {
      String errorMessage = l10n.roleChangeFailed;
      if (e.toString().contains('登录已过期')) {
        errorMessage = l10n.loginExpired;
        // 自动跳转到登录页面
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go(AppRoutes.login);
          }
        });
      } else if (e.toString().contains('Unauthorized access')) {
        errorMessage = l10n.unauthorizedRoleChange;
      } else if (e.toString().contains('Network error')) {
        errorMessage = l10n.networkError;
      } else {
        errorMessage = '${l10n.roleChangeFailed}: ${e.toString().replaceAll('Exception: ', '')}';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Future<void> _toggleUserStatus(User user) async {
    final l10n = AppLocalizations.of(context)!;
    final action = user.isActive ? l10n.banUser : l10n.unbanUser;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(user.isActive ? l10n.confirmBan : l10n.confirmUnban),
        content: Text(user.isActive
            ? l10n.confirmBanUser(user.username)
            : l10n.confirmUnbanUser(user.username)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: user.isActive ? Colors.red : Colors.green,
            ),
            child: Text(action),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.toggleUserStatus(user.id, !user.isActive);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: user.isActive ? l10n.banSuccess : l10n.unbanSuccess,
          message: user.isActive
              ? l10n.userBanned(user.username)
              : l10n.userUnbanned(user.username),
          onComplete: () {
            _loadUsers();
          },
        );
      } catch (e) {
        String errorMessage = user.isActive ? l10n.banFailed : l10n.unbanFailed;
        if (e.toString().contains('登录已过期')) {
          errorMessage = l10n.loginExpired;
          // 自动跳转到登录页面
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              context.go(AppRoutes.login);
            }
          });
        } else if (e.toString().contains('Unauthorized access')) {
          errorMessage = user.isActive ? l10n.unauthorizedBan : l10n.unauthorizedUnban;
        } else if (e.toString().contains('Network error')) {
          errorMessage = l10n.networkError;
        } else {
          errorMessage = '${user.isActive ? l10n.banFailed : l10n.unbanFailed}: ${e.toString().replaceAll('Exception: ', '')}';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _exportToExcel() async {
    final l10n = AppLocalizations.of(context)!;
    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.noUsersToExport),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 显示保存位置选择对话框
    final saveToDownload = await _showSaveLocationDialog();
    if (saveToDownload == null) return; // 用户取消了选择
    
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(l10n.exportingExcel),
            ],
          ),
        ),
      );

      // 获取所有用户数据（不分页）
      final allUsersQuery = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        roles: _appliedRoles.isNotEmpty ? _appliedRoles : null,
        departments: _appliedDepartments.isNotEmpty ? _appliedDepartments : null,
        pageNumber: 1,
        pageSize: 1000, // 获取大量数据
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );
      
      final allUsersResponse = await _userService.getUsers(allUsersQuery);

      // 导出Excel
      final filePath = await ExcelService.exportUsersToExcel(
        allUsersResponse.users,
        saveToDownload: saveToDownload,
      );
      
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: l10n.exportSuccess,
          message: l10n.excelSavedTo(filePath),
          onComplete: () {
            // 成功动画完成后的回调
          },
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n.exportFailed}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveLocationDialog() async {
    final l10n = AppLocalizations.of(context)!;
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n.selectSaveLocation),
          content: Text(l10n.selectExcelSaveLocation),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: Text(l10n.cancel),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(l10n.appDirectory),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(l10n.downloadFolder),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Stack(
        children: [
          // 主要内容
          MainLayout(
            currentRoute: '/users',
            child: Column(
              children: [
                // 现代化工具栏
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        spreadRadius: 0,
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.02),
                        spreadRadius: 0,
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 现代化工具栏
                      _buildModernToolbar(),
                    ],
                  ),
                ),

                // 条件性显示统计信息（只在有已应用的筛选条件时显示）
                if (_hasActiveFilters)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[100]!, width: 1),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.blue[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.analytics_rounded,
                            color: Colors.blue[600],
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _userListResponse != null
                                ? AppLocalizations.of(context)!.totalRecords(_userListResponse!.totalCount)
                                : AppLocalizations.of(context)!.totalRecords(0),
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (_isLoading)
                          SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                            ),
                          ),
                        const Spacer(),
                        // 清除筛选按钮
                        GestureDetector(
                          onTap: _clearFilters,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.clear_rounded,
                                  color: Colors.blue[600],
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  AppLocalizations.of(context)!.clearFilters,
                                  style: TextStyle(
                                    color: Colors.blue[600],
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // 用户列表
                Expanded(
                  child: _buildUserList(),
                ),
                // 分页
                if (_userListResponse != null) _buildPagination(),
              ],
            ),
          ),
          // 返回顶部按钮
          if (_showScrollToTop)
            Positioned(
              right: 16,
              bottom: 100, // 距离底部100像素，避免与底部导航栏重叠
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: FloatingActionButton.small(
                  onPressed: _scrollToTop,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  child: const Icon(Icons.keyboard_arrow_up_rounded, color: Colors.white, size: 24),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 现代化工具栏
  Widget _buildModernToolbar() {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        // 动画搜索框
        Expanded(
          child: AnimatedSearchBar(
            controller: _searchController,
            hintText: l10n.searchUsersHint,
            isSearching: _isSearching,
            onChanged: (value) {
              setState(() {}); // 更新UI
            },
            onClear: () {
              setState(() {}); // 更新UI
            },
          ),
        ),
        const SizedBox(width: 12),
        // 筛选按钮
        Container(
          decoration: BoxDecoration(
            color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                ? Colors.blue[50]
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                  ? Colors.blue[200]!
                  : Colors.grey[200]!,
              width: 1
            ),
          ),
          child: Stack(
            children: [
              IconButton(
                onPressed: _showFilterBottomSheet,
                icon: Icon(
                  Icons.tune_rounded,
                  color: (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                      ? Colors.blue[600]
                      : Colors.grey[600],
                  size: 22
                ),
                tooltip: l10n.filterTooltip,
              ),
              if (_appliedRoles.isNotEmpty || _appliedDepartments.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.red[500],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
        // 只有管理员才能看到导出和添加按钮
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final currentUser = authProvider.currentUser;
            
            if (currentUser == null || !currentUser.isAdmin) {
              return const SizedBox.shrink();
            }
            
            return Row(
              children: [
                const SizedBox(width: 8),
                // 导出按钮
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.green[600]!, Colors.green[700]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: _exportToExcel,
                    icon: const Icon(Icons.download_rounded, color: Colors.white, size: 22),
                    tooltip: l10n.exportExcelTooltip,
                  ),
                ),
                const SizedBox(width: 8),
                // 添加用户按钮
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue[600]!, Colors.blue[700]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: () => _navigateToUserForm(),
                    icon: const Icon(Icons.person_add_rounded, color: Colors.white, size: 22),
                    tooltip: l10n.addUserTooltip,
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }





  // 筛选底部弹窗
  void _showFilterBottomSheet() {
    final l10n = AppLocalizations.of(context)!;
    // 创建临时变量来存储弹窗内的选择
    List<String> tempSelectedRoles = List.from(_selectedRoles);
    List<String> tempSelectedDepartments = List.from(_selectedDepartments);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(Icons.tune_rounded, color: Colors.blue[600], size: 24),
                  const SizedBox(width: 12),
                  Text(
                    l10n.filterConditions,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // 角色筛选
              Text(
                l10n.roleLabel,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip(l10n.allRoles, null, tempSelectedRoles, (newSelection) {
                    setModalState(() {
                      tempSelectedRoles = newSelection;
                    });
                  }),
                  ..._roles.map((role) =>
                    _buildMultiSelectChip(
                      role == 'Admin' ? l10n.administrator : l10n.regularUser,
                      role,
                      tempSelectedRoles,
                      (newSelection) {
                        setModalState(() {
                          tempSelectedRoles = newSelection;
                        });
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // 部门筛选
              Text(
                l10n.departmentLabel,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildMultiSelectChip(l10n.allDepartments, null, tempSelectedDepartments, (newSelection) {
                    setModalState(() {
                      tempSelectedDepartments = newSelection;
                    });
                  }),
                  ..._departments.map((dept) =>
                    _buildMultiSelectChip(
                      _getDepartmentDisplayName(dept),
                      dept,
                      tempSelectedDepartments,
                      (newSelection) {
                        setModalState(() {
                          tempSelectedDepartments = newSelection;
                        });
                      }
                    )
                  ),
                ],
              ),
              const SizedBox(height: 32),
              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          tempSelectedRoles = [];
                          tempSelectedDepartments = [];
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(l10n.resetFilters),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // 应用临时选择到实际状态
                        setState(() {
                          _selectedRoles = tempSelectedRoles;
                          _selectedDepartments = tempSelectedDepartments;
                        });
                        _onFilterChanged();
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(l10n.applyFilters),
                    ),
                  ),
                ],
              ),
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        ),
      ),
    );
  }

  // 新增：构建多选筛选芯片（支持多选）
  Widget _buildMultiSelectChip(String label, String? value, List<String> selectedValues, Function(List<String>) onTap) {
    final isSelected = value == null ? selectedValues.isEmpty : selectedValues.contains(value);

    return GestureDetector(
      onTap: () {
        List<String> newSelection = List.from(selectedValues);
        if (value == null) {
          // "全部类型" 被点击，清空所有选择
          newSelection.clear();
        } else {
          // 具体类型被点击，切换选择状态
          if (newSelection.contains(value)) {
            newSelection.remove(value);
          } else {
            newSelection.add(value);
          }
        }
        onTap(newSelection);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue[600]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildFilterDropdown<T>({
    required T? value,
    required String hint,
    required IconData icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          isExpanded: true,
          hint: Row(
            children: [
              Icon(icon, color: Colors.grey[500], size: 18),
              const SizedBox(width: 12),
              Text(
                hint,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
          icon: Icon(Icons.keyboard_arrow_down_rounded, color: Colors.grey[500], size: 20),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          items: items,
          onChanged: onChanged,
          style: const TextStyle(color: Colors.black87, fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildUserList() {
    final l10n = AppLocalizations.of(context)!;
    if (_isLoading && (_userListResponse == null || _userListResponse!.users.isEmpty)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(32),
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                    strokeWidth: 3,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                l10n.loadingUsers,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Icon(Icons.error_outline_rounded, size: 40, color: Colors.red[400]),
              ),
              const SizedBox(height: 24),
              Text(
                l10n.loadFailed,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  if (!authProvider.isLoggedIn) {
                    return Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue[600]!, Colors.blue[700]!],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await authProvider.initializeAuth();
                          if (authProvider.isLoggedIn) {
                            _loadUsers();
                          } else {
                            if (mounted) {
                              context.go(AppRoutes.login);
                            }
                          }
                        },
                        icon: const Icon(Icons.login_rounded),
                        label: Text(l10n.relogin),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    );
                  } else {
                    return Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue[600]!, Colors.blue[700]!],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: _loadUsers,
                        icon: const Icon(Icons.refresh_rounded),
                        label: Text(l10n.reload),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      );
    }

    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(Icons.people_outline_rounded, size: 50, color: Colors.grey[400]),
              ),
              const SizedBox(height: 24),
              Text(
                _hasActiveFilters ? l10n.noUsersFound : l10n.noUsersData,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _hasActiveFilters ? l10n.adjustFilters : l10n.addFirstUser,
                style: TextStyle(color: Colors.grey[500], fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _userListResponse!.users.length,
      itemBuilder: (context, index) {
        final user = _userListResponse!.users[index];
        return _buildModernUserCard(user);
      },
    );
  }

  Widget _buildModernUserCard(User user) {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToUserDetail(user),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户基本信息行
                Row(
                  children: [
                    // 现代化头像
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        gradient: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                            ? null
                            : LinearGradient(
                                colors: user.isActive
                                    ? [_getRoleColor(user.role).withOpacity(0.8), _getRoleColor(user.role)]
                                    : [Colors.grey[400]!, Colors.grey[600]!],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                        boxShadow: [
                          BoxShadow(
                            color: (user.isActive ? _getRoleColor(user.role) : Colors.grey).withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(14),
                              child: Image.network(
                                _getFullAvatarUrl(user.avatarUrl!),
                                width: 48,
                                height: 48,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // 如果头像加载失败，显示角色图标
                                  return Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(14),
                                      gradient: LinearGradient(
                                        colors: user.isActive
                                            ? [_getRoleColor(user.role).withOpacity(0.8), _getRoleColor(user.role)]
                                            : [Colors.grey[400]!, Colors.grey[600]!],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Center(
                                      child: user.role == UserRole.admin
                                          ? const FaIcon(
                                              FontAwesomeIcons.userShield,
                                              color: Colors.white,
                                              size: 20,
                                            )
                                          : const FaIcon(
                                              FontAwesomeIcons.user,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                    ),
                                  );
                                },
                              ),
                            )
                          : Center(
                              child: user.role == UserRole.admin
                                  ? const FaIcon(
                                      FontAwesomeIcons.userShield,
                                      color: Colors.white,
                                      size: 20,
                                    )
                                  : const FaIcon(
                                      FontAwesomeIcons.user,
                                      color: Colors.white,
                                      size: 18,
                                    ),
                            ),
                    ),
                    const SizedBox(width: 16),
                    // 用户信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  user.fullName ?? user.username,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: user.isActive ? Colors.black87 : Colors.grey[600],
                                    letterSpacing: -0.2,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              // 状态标签
                              if (!user.isActive)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.red[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.red[200]!, width: 1),
                                  ),
                                  child: Text(
                                    l10n.userBannedStatus,
                                    style: TextStyle(
                                      color: Colors.red[600],
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            user.email,
                            style: TextStyle(
                              color: user.isActive ? Colors.grey[600] : Colors.grey[500],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.purple[50],
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.purple[200]!, width: 1),
                            ),
                            child: Text(
                              'ID: ${user.displayId}',
                              style: TextStyle(
                                color: Colors.purple[700],
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 操作菜单
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        final currentUser = authProvider.currentUser;
                        
                        // 只有管理员才能看到操作菜单
                        if (currentUser == null || !currentUser.isAdmin) {
                          return const SizedBox(width: 24);
                        }
                        
                        final isCurrentUser = currentUser.id == user.id;
                        final isTargetAdmin = user.role == UserRole.admin;
                        
                        // 不能对自己或其他管理员进行某些操作
                        final canChangeRole = !isCurrentUser && !isTargetAdmin;
                        final canToggleStatus = !isCurrentUser && !isTargetAdmin;
                        final canDelete = !isCurrentUser && !isTargetAdmin;
                        final hasAnyAction = canChangeRole || canToggleStatus || canDelete;
                        
                        if (!hasAnyAction) {
                          return const SizedBox(width: 24);
                        }
                        
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: PopupMenuButton<String>(
                            icon: Icon(Icons.more_horiz_rounded, color: Colors.grey[600], size: 20),
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            onSelected: (value) {
                              switch (value) {
                                case 'change_role':
                                  _showChangeRoleDialog(user);
                                  break;
                                case 'toggle_status':
                                  _toggleUserStatus(user);
                                  break;
                                case 'delete':
                                  _deleteUser(user);
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              if (canChangeRole)
                                PopupMenuItem(
                                  value: 'change_role',
                                  child: Row(
                                    children: [
                                      const Icon(Icons.admin_panel_settings_rounded, size: 18),
                                      const SizedBox(width: 12),
                                      Text(l10n.changeRole),
                                    ],
                                  ),
                                ),
                              if (canToggleStatus)
                                PopupMenuItem(
                                  value: 'toggle_status',
                                  child: Row(
                                    children: [
                                      Icon(
                                        user.isActive ? Icons.block_rounded : Icons.check_circle_rounded,
                                        size: 18,
                                        color: user.isActive ? Colors.red : Colors.green,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        user.isActive ? l10n.banUser : l10n.unbanUser,
                                        style: TextStyle(
                                          color: user.isActive ? Colors.red : Colors.green,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              if (canDelete)
                                PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      const Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                                      const SizedBox(width: 12),
                                      Text(l10n.deleteUser, style: const TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 信息标签行
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildModernChip(l10n.roleLabel, user.role == UserRole.admin ? l10n.administrator : l10n.regularUser, _getRoleColor(user.role)),
                    if (user.department != null && user.department!.isNotEmpty)
                      _buildModernChip(l10n.departmentLabel, _getDepartmentDisplayName(user.department!), Colors.indigo),
                    _buildModernChip(l10n.assetsCount, user.assignedAssetsCount.toString(), Colors.amber[700]!),
                  ],
                ),
                const SizedBox(height: 12),
                // 底部信息
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey[200]!, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.schedule_rounded, color: Colors.grey[600], size: 16),
                      const SizedBox(width: 8),
                      Text(
                        l10n.createdAt(TimezoneUtils.formatDateTime(user.createdAt)),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }



  // 获取完整的头像URL
  String _getFullAvatarUrl(String avatarUrl) {
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      return avatarUrl;
    } else {
      // 相对URL，需要添加基础URL
      return 'http://********:5000$avatarUrl';
    }
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'normal user':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildPagination() {
    final l10n = AppLocalizations.of(context)!;
    final response = _userListResponse!;
    final totalPages = response.totalPages;

    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline_rounded, color: Colors.grey[600], size: 16),
                const SizedBox(width: 8),
                Text(
                  l10n.totalRecords(response.totalCount),
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _currentPage > 1 ? Colors.blue[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _currentPage > 1 ? Colors.blue[200]! : Colors.grey[200]!,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: _currentPage > 1 ? () => _onPageChanged(_currentPage - 1) : null,
                  icon: Icon(
                    Icons.chevron_left_rounded,
                    color: _currentPage > 1 ? Colors.blue[600] : Colors.grey[400],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Text(
                  '$_currentPage / $totalPages',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: _currentPage < totalPages ? Colors.blue[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: _currentPage < totalPages ? Colors.blue[200]! : Colors.grey[200]!,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: _currentPage < totalPages ? () => _onPageChanged(_currentPage + 1) : null,
                  icon: Icon(
                    Icons.chevron_right_rounded,
                    color: _currentPage < totalPages ? Colors.blue[600] : Colors.grey[400],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 